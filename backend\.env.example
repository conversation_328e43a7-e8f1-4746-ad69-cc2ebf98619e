# Copy this file to .env and fill in your values

# Environment Mode
# Valid values: local, staging, production
ENV_MODE=local

#DATABASE
SUPABASE_URL=https://otvwwaxpuaywkzhlmini.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.hNuyAHzQHDcM7SniRVdZna8qf59U-WVMQVO6edvjHNc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.1uR04_-NZkckqeU8nPpToaJKQshYBfpvs19O_ZHRv-8

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SSL=false

RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672

# LLM Providers:
ANTHROPIC_API_KEY=
OPENAI_API_KEY=********************************************************************************************************************************************************************
MODEL_TO_USE=ChatGpt 4O

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

GROQ_API_KEY=********************************************************

OPENROUTER_API_KEY=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


# DATA APIS
RAPID_API_KEY=

# WEB SEARCH
TAVILY_API_KEY=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


# WEB SCRAPE
# FIRECRAWL_API_KEY=fc-c5e756905b8247d2a88e0910bd526c56  # Replace with your own valid Firecrawl API key
FIRECRAWL_API_KEY=
FIRECRAWL_URL=https://api.firecrawl.dev

# Sandbox container provider:
DAYTONA_API_KEY=dtn_d4a802d1498d2167dd2775d00ba0810aaedc80eea4b57c8d07bd0e9b9cca44d3
DAYTONA_SERVER_URL=https://app.daytona.io/api
DAYTONA_TARGET=us

LANGFUSE_PUBLIC_KEY="pk-REDACTED"
LANGFUSE_SECRET_KEY="sk-REDACTED"
LANGFUSE_HOST="https://cloud.langfuse.com"

SMITHERY_API_KEY=