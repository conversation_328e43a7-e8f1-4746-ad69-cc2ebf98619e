services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
      worker:
        condition: service_started
    env_file:
    - ./backend/.env
    environment:
    - REDIS_HOST=redis
    - REDIS_PORT=6379
    - REDIS_PASSWORD=
    - REDIS_SSL=False
    - RABBITMQ_HOST=rabbitmq
    - RABBITMQ_PORT=5672
    - FRONTEND_URL=http://localhost:3000
    ports:
    - 8000:8000
    volumes:
    - ./backend/.env:/app/.env:ro
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    command:
    - npm
    - run
    - start
    depends_on:
    - backend
    environment:
    - NODE_ENV=production
    init: true
    ports:
    - 3000:3000
    volumes:
    - ./frontend/.env.local:/app/.env.local:ro
  rabbitmq:
    healthcheck:
      interval: 10s
      retries: 5
      start_period: 10s
      test:
      - CMD
      - rabbitmq-diagnostics
      - -q
      - ping
      timeout: 5s
    image: rabbitmq
    ports:
    - 25672:5672
    - 15672:15672
    restart: unless-stopped
    volumes:
    - rabbitmq_data:/var/lib/rabbitmq
  redis:
    command: redis-server /usr/local/etc/redis/redis.conf --save 60 1 --loglevel warning
    healthcheck:
      interval: 10s
      retries: 3
      test:
      - CMD
      - redis-cli
      - ping
      timeout: 5s
    image: redis:7-alpine
    ports:
    - 6379:6379
    volumes:
    - redis_data:/data
    - ./backend/services/docker/redis.conf:/usr/local/etc/redis/redis.conf:ro
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: python -m dramatiq --skip-logging run_agent_background
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
    - ./backend/.env
    environment:
    - REDIS_HOST=redis
    - REDIS_PORT=6379
    - REDIS_PASSWORD=
    - REDIS_SSL=False
    - RABBITMQ_HOST=rabbitmq
    - RABBITMQ_PORT=5672
    volumes:
    - ./backend/.env:/app/.env:ro
volumes:
  rabbitmq_data: null
  redis_data: null
