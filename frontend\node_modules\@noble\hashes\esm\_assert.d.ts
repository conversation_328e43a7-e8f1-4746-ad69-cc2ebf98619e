/**
 * Internal assertion helpers.
 * @module
 * @deprecated
 */
import { abytes as ab, aexists as ae, anumber as an, aoutput as ao, type IHash as H } from './utils.ts';
/** @deprecated Use import from `noble/hashes/utils` module */
export declare const abytes: typeof ab;
/** @deprecated Use import from `noble/hashes/utils` module */
export declare const aexists: typeof ae;
/** @deprecated Use import from `noble/hashes/utils` module */
export declare const anumber: typeof an;
/** @deprecated Use import from `noble/hashes/utils` module */
export declare const aoutput: typeof ao;
/** @deprecated Use import from `noble/hashes/utils` module */
export type Hash = H;
//# sourceMappingURL=_assert.d.ts.map