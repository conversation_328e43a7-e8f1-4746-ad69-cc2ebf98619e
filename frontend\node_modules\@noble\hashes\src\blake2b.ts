/**
 * Blake2b hash function. Focuses on 64-bit platforms, but in JS speed different from Blake2s is negligible.
 * @module
 * @deprecated
 */
import { BLAKE2b as B2B, blake2b as b2b } from './blake2.ts';
/** @deprecated Use import from `noble/hashes/blake2` module */
export const BLAKE2b: typeof B2B = B2B;
/** @deprecated Use import from `noble/hashes/blake2` module */
export const blake2b: typeof b2b = b2b;
