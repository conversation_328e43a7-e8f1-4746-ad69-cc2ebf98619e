/**
 * SHA2-512 a.k.a. sha512 and sha384. It is slower than sha256 in js because u64 operations are slow.
 *
 * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and
 * [the paper on truncated SHA512/256](https://eprint.iacr.org/2010/548.pdf).
 * @module
 * @deprecated
 */
import {
  SHA384 as SHA384n,
  sha384 as sha384n,
  sha512_224 as sha512_224n,
  SHA512_224 as SHA512_224n,
  sha512_256 as sha512_256n,
  SHA512_256 as SHA512_256n,
  SHA512 as SHA512n,
  sha512 as sha512n,
} from './sha2.ts';
/** @deprecated Use import from `noble/hashes/sha2` module */
export const SHA512: typeof SHA512n = SHA512n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const sha512: typeof sha512n = sha512n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const SHA384: typeof SHA384n = SHA384n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const sha384: typeof sha384n = sha384n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const SHA512_224: typeof SHA512_224n = SHA512_224n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const sha512_224: typeof sha512_224n = sha512_224n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const SHA512_256: typeof SHA512_256n = SHA512_256n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const sha512_256: typeof sha512_256n = sha512_256n;
