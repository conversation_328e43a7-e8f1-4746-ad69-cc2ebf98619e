{"name": "@types/formidable", "version": "3.4.5", "description": "TypeScript definitions for formidable", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/formidable", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "Nemo157", "url": "https://github.com/Nemo157"}, {"name": "<PERSON>", "githubUsername": "martin-badin", "url": "https://github.com/martin-badin"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/devLana"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/formidable"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "159a1ee6e7a9a383cafa7e1ff2837875527f157bef133759936f5645e692bed8", "typeScriptVersion": "4.5"}