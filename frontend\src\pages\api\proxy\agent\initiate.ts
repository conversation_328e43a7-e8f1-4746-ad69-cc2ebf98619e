import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import FormData from 'form-data';
import fetch from 'node-fetch';
import fs from 'fs';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the incoming form data
    const form = formidable({});
    const [fields, files] = await form.parse(req);

    // Get the backend URL from environment
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://backend:8000';
    
    // Create a new FormData object for the backend request
    const formData = new FormData();
    
    // Add fields to form data
    Object.entries(fields).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(v => formData.append(key, v));
      } else {
        formData.append(key, value || '');
      }
    });

    // Add files to form data
    Object.entries(files).forEach(([key, fileArray]) => {
      if (Array.isArray(fileArray)) {
        fileArray.forEach(file => {
          if (file.filepath) {
            formData.append(key, fs.createReadStream(file.filepath), {
              filename: file.originalFilename || 'file',
              contentType: file.mimetype || 'application/octet-stream',
            });
          }
        });
      }
    });

    // Get authorization header from the request
    const authHeader = req.headers.authorization;
    
    // Make request to backend
    const response = await fetch(`${backendUrl}/api/agent/initiate`, {
      method: 'POST',
      headers: {
        ...(authHeader && { Authorization: authHeader }),
        ...formData.getHeaders(),
      },
      body: formData,
    });

    // Forward the response
    const responseText = await response.text();
    
    res.status(response.status);
    
    // Set response headers
    response.headers.forEach((value, key) => {
      if (key.toLowerCase() !== 'content-encoding') {
        res.setHeader(key, value);
      }
    });

    if (response.headers.get('content-type')?.includes('application/json')) {
      try {
        const jsonData = JSON.parse(responseText);
        res.json(jsonData);
      } catch {
        res.send(responseText);
      }
    } else {
      res.send(responseText);
    }
  } catch (error) {
    console.error('Proxy error:', error);
    res.status(500).json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}
