# Suna Quick Start Script
# This script sets up everything needed to run Suna with desktop shortcuts

param(
    [switch]$SkipShortcuts,
    [switch]$Help
)

function Show-Help {
    Write-Host "Suna Quick Start Script" -ForegroundColor Cyan
    Write-Host "This script will:" -ForegroundColor Yellow
    Write-Host "  1. Verify all prerequisites are installed" -ForegroundColor White
    Write-Host "  2. Run the Suna setup wizard" -ForegroundColor White
    Write-Host "  3. Create desktop shortcuts" -ForegroundColor White
    Write-Host "  4. Test the installation" -ForegroundColor White
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  quick_start.ps1              - Full setup with shortcuts" -ForegroundColor Green
    Write-Host "  quick_start.ps1 -SkipShortcuts - Setup without creating shortcuts" -ForegroundColor Green
    Write-Host "  quick_start.ps1 -Help        - Show this help" -ForegroundColor Green
    Write-Host ""
}

function Test-Prerequisites {
    Write-Host "Checking prerequisites..." -ForegroundColor Cyan
    $issues = @()
    
    # Check Python
    try {
        $null = python --version 2>$null
        Write-Host "[OK] Python is installed" -ForegroundColor Green
    } catch {
        $issues += "Python is not installed or not in PATH"
        Write-Host "[ERROR] Python not found" -ForegroundColor Red
    }
    
    # Check Docker
    try {
        $null = docker --version 2>$null
        Write-Host "[OK] Docker is installed" -ForegroundColor Green
        
        # Check if Docker is running
        try {
            $null = docker info 2>$null
            Write-Host "[OK] Docker is running" -ForegroundColor Green
        } catch {
            $issues += "Docker is installed but not running"
            Write-Host "[ERROR] Docker is not running" -ForegroundColor Red
        }
    } catch {
        $issues += "Docker is not installed"
        Write-Host "[ERROR] Docker not found" -ForegroundColor Red
    }
    
    # Check Git
    try {
        $null = git --version 2>$null
        Write-Host "[OK] Git is installed" -ForegroundColor Green
    } catch {
        $issues += "Git is not installed"
        Write-Host "[ERROR] Git not found" -ForegroundColor Red
    }
    
    # Check Node.js
    try {
        $null = node --version 2>$null
        Write-Host "[OK] Node.js is installed" -ForegroundColor Green
    } catch {
        $issues += "Node.js is not installed"
        Write-Host "[ERROR] Node.js not found" -ForegroundColor Red
    }
    
    # Check npm
    try {
        $null = npm --version 2>$null
        Write-Host "[OK] npm is installed" -ForegroundColor Green
    } catch {
        $issues += "npm is not installed"
        Write-Host "[ERROR] npm not found" -ForegroundColor Red
    }
    
    if ($issues.Count -gt 0) {
        Write-Host ""
        Write-Host "[ERROR] Prerequisites not met:" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "  - $issue" -ForegroundColor Yellow
        }
        Write-Host ""
        Write-Host "Please install the missing prerequisites and try again." -ForegroundColor Yellow
        Write-Host "See INSTALLATION_GUIDE.md for download links." -ForegroundColor Cyan
        return $false
    }
    
    Write-Host ""
    Write-Host "[SUCCESS] All prerequisites are installed!" -ForegroundColor Green
    return $true
}

function Start-Setup {
    Write-Host ""
    Write-Host "Starting Suna setup wizard..." -ForegroundColor Cyan
    Write-Host "You will need to provide API keys for various services." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        $process = Start-Process -FilePath "python" -ArgumentList "setup.py" -NoNewWindow -PassThru -Wait
        
        if ($process.ExitCode -eq 0) {
            Write-Host "[SUCCESS] Setup completed successfully!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[ERROR] Setup failed or was cancelled" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "[ERROR] Failed to run setup: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function New-Shortcuts {
    Write-Host ""
    Write-Host "Creating desktop shortcuts..." -ForegroundColor Cyan
    
    try {
        $process = Start-Process -FilePath "powershell" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", "create_desktop_shortcut.ps1" -NoNewWindow -PassThru -Wait
        
        if ($process.ExitCode -eq 0) {
            Write-Host "[SUCCESS] Desktop shortcuts created!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[ERROR] Failed to create shortcuts" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "[ERROR] Failed to create shortcuts: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-Installation {
    Write-Host ""
    Write-Host "Testing installation..." -ForegroundColor Cyan
    
    # Check if setup files exist
    if (-not (Test-Path "backend\.env")) {
        Write-Host "[ERROR] Backend configuration not found" -ForegroundColor Red
        return $false
    }
    
    if (-not (Test-Path "frontend\.env.local")) {
        Write-Host "[ERROR] Frontend configuration not found" -ForegroundColor Red
        return $false
    }
    
    Write-Host "[OK] Configuration files found" -ForegroundColor Green
    
    # Test the launcher
    try {
        Write-Host "Testing launcher..." -ForegroundColor Yellow
        $process = Start-Process -FilePath "python" -ArgumentList "suna_launcher.py", "--help" -NoNewWindow -PassThru -Wait
        
        if ($process.ExitCode -eq 0) {
            Write-Host "[OK] Launcher is working" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Launcher test failed" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[WARNING] Could not test launcher" -ForegroundColor Yellow
    }
    
    Write-Host "[SUCCESS] Installation test completed!" -ForegroundColor Green
    return $true
}

# Main script
try {
    $Host.UI.RawUI.WindowTitle = "Suna Quick Start"
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Cyan
    Write-Host "              Suna Quick Start" -ForegroundColor Cyan
    Write-Host "===============================================" -ForegroundColor Cyan
    Write-Host ""
    
    if ($Help) {
        Show-Help
        exit 0
    }
    
    # Check if we're in the right directory
    if (-not (Test-Path "docker-compose.yaml")) {
        Write-Host "[ERROR] Not in Suna directory" -ForegroundColor Red
        Write-Host "Please run this script from the Suna installation directory." -ForegroundColor Yellow
        Read-Host "Press Enter to close"
        exit 1
    }
    
    # Step 1: Check prerequisites
    Write-Host "Step 1: Checking Prerequisites" -ForegroundColor Blue
    Write-Host "==============================" -ForegroundColor Blue
    if (-not (Test-Prerequisites)) {
        Read-Host "Press Enter to close"
        exit 1
    }
    
    # Step 2: Run setup
    Write-Host ""
    Write-Host "Step 2: Running Setup Wizard" -ForegroundColor Blue
    Write-Host "=============================" -ForegroundColor Blue
    if (-not (Start-Setup)) {
        Write-Host "Setup is required to continue." -ForegroundColor Yellow
        Read-Host "Press Enter to close"
        exit 1
    }
    
    # Step 3: Create shortcuts (optional)
    if (-not $SkipShortcuts) {
        Write-Host ""
        Write-Host "Step 3: Creating Desktop Shortcuts" -ForegroundColor Blue
        Write-Host "===================================" -ForegroundColor Blue
        $shortcutSuccess = New-Shortcuts
        if (-not $shortcutSuccess) {
            Write-Host "Shortcuts creation failed, but you can create them later." -ForegroundColor Yellow
        }
    } else {
        Write-Host ""
        Write-Host "Step 3: Skipping Desktop Shortcuts" -ForegroundColor Blue
        Write-Host "===================================" -ForegroundColor Blue
        Write-Host "Desktop shortcuts skipped as requested." -ForegroundColor Yellow
    }
    
    # Step 4: Test installation
    Write-Host ""
    Write-Host "Step 4: Testing Installation" -ForegroundColor Blue
    Write-Host "=============================" -ForegroundColor Blue
    Test-Installation
    
    # Final success message
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host "           SETUP COMPLETE!" -ForegroundColor Green
    Write-Host "===============================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Suna is now ready to use!" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "To start Suna:" -ForegroundColor Yellow
    if (-not $SkipShortcuts) {
        Write-Host "  - Double-click the 'Suna' shortcut on your desktop" -ForegroundColor White
        Write-Host "  - Or run: python suna_launcher.py" -ForegroundColor White
    } else {
        Write-Host "  - Run: python suna_launcher.py" -ForegroundColor White
        Write-Host "  - Or create shortcuts: powershell -ExecutionPolicy Bypass -File create_desktop_shortcut.ps1" -ForegroundColor White
    }
    Write-Host ""
    Write-Host "The launcher will automatically:" -ForegroundColor Green
    Write-Host "  - Find available ports" -ForegroundColor White
    Write-Host "  - Start all services" -ForegroundColor White
    Write-Host "  - Open your browser" -ForegroundColor White
    Write-Host ""
    Write-Host "For help, see INSTALLATION_GUIDE.md" -ForegroundColor Cyan
    
} catch {
    Write-Host "[ERROR] Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Read-Host "Press Enter to close"
