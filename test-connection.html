<!DOCTYPE html>
<html>
<head>
    <title>Backend Connection Test</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' http://localhost:8000 http://127.0.0.1:8000; connect-src 'self' http://localhost:8000 http://127.0.0.1:8000;">
</head>
<body>
    <h1>Backend Connection Test</h1>
    <button onclick="testHealth()">Test Health Endpoint</button>
    <button onclick="testAgentInitiate()">Test Agent Initiate</button>
    <button onclick="testBackendReachability()">Test Backend Reachability</button>
    <button onclick="testSimpleFetch()">Test Simple Fetch</button>
    <div id="results"></div>

    <h2>Browser Console Tests</h2>
    <p>Open browser console (F12) and run these commands one by one:</p>
    <div>
        <h3>Test 1: Basic Health Check</h3>
        <code>fetch('http://localhost:8000/api/health').then(r => r.json()).then(console.log).catch(console.error)</code>
    </div>
    <div>
        <h3>Test 2: With CORS Mode</h3>
        <code>fetch('http://localhost:8000/api/health', {mode: 'cors'}).then(r => r.json()).then(console.log).catch(console.error)</code>
    </div>
    <div>
        <h3>Test 3: Agent Initiate (without auth)</h3>
        <code>
            const fd = new FormData(); fd.append('prompt', 'test');
            fetch('http://localhost:8000/api/agent/initiate', {method: 'POST', body: fd}).then(r => r.text()).then(console.log).catch(console.error)
        </code>
    </div>
    <div>
        <h3>Test 4: Check if localhost resolves</h3>
        <code>fetch('http://127.0.0.1:8000/api/health').then(r => r.json()).then(console.log).catch(console.error)</code>
    </div>
    <div>
        <h3>Test 5: Try port 8080</h3>
        <code>fetch('http://localhost:8080/api/health').then(r => r.json()).then(console.log).catch(console.error)</code>
    </div>
    <div>
        <h3>Test 6: Agent initiate on port 8080</h3>
        <code>
            const fd = new FormData(); fd.append('prompt', 'test');
            fetch('http://localhost:8080/api/agent/initiate', {method: 'POST', body: fd}).then(r => r.text()).then(console.log).catch(console.error)
        </code>
    </div>
    <div>
        <h3>Test 7: Check Supabase Auth Status</h3>
        <code>
            // Check if user is logged in to Supabase
            fetch('http://localhost:3000').then(() => {
                console.log('Checking Supabase auth status...');
                // This will show if there's a session
                console.log('Check browser Application tab > Local Storage > supabase.auth.token');
            });
        </code>
    </div>
    </div>

    <script>
        async function testHealth() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing health endpoint...';
            
            try {
                const response = await fetch('http://localhost:8000/api/health');
                const data = await response.json();
                results.innerHTML = `<h3>Health Test Success:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                results.innerHTML = `<h3>Health Test Error:</h3><pre>${error.message}</pre>`;
            }
        }

        async function testAgentInitiate() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing agent initiate endpoint...';

            try {
                console.log('Testing agent initiate with URL: http://localhost:8000/api/agent/initiate');

                const formData = new FormData();
                formData.append('prompt', 'test prompt');

                const response = await fetch('http://localhost:8000/api/agent/initiate', {
                    method: 'POST',
                    body: formData,
                    mode: 'cors'
                });

                const data = await response.text();
                results.innerHTML = `<h3>Agent Initiate Test (Status: ${response.status}):</h3><pre>${data}</pre>`;
            } catch (error) {
                console.error('Agent initiate error:', error);
                results.innerHTML = `<h3>Agent Initiate Test Error:</h3><pre>Error: ${error.message}\nStack: ${error.stack}</pre>`;
            }
        }

        // Test if we can reach the backend at all
        async function testBackendReachability() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing backend reachability...';

            try {
                // Test different URLs to see which one works
                const urls = [
                    'http://localhost:8000/api/health',
                    'http://127.0.0.1:8000/api/health',
                    'http://backend:8000/api/health'
                ];

                for (const url of urls) {
                    try {
                        console.log(`Testing URL: ${url}`);
                        const response = await fetch(url, { mode: 'cors' });
                        const data = await response.json();
                        results.innerHTML += `<h4>✅ SUCCESS: ${url}</h4><pre>${JSON.stringify(data, null, 2)}</pre>`;
                        return; // Stop on first success
                    } catch (error) {
                        console.error(`Failed ${url}:`, error);
                        results.innerHTML += `<h4>❌ FAILED: ${url}</h4><pre>${error.message}</pre>`;
                    }
                }
            } catch (error) {
                results.innerHTML += `<h3>Overall Error:</h3><pre>${error.message}</pre>`;
            }
        }

        async function testSimpleFetch() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing simple fetch...';

            try {
                console.log('Testing simple fetch to backend...');
                const response = await fetch('http://localhost:8000/api/health', {
                    method: 'GET',
                    mode: 'cors',
                    cache: 'no-cache'
                });

                console.log('Response received:', response);
                const data = await response.json();
                console.log('Data received:', data);

                results.innerHTML = `<h3>✅ Simple Fetch Success:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                console.error('Simple fetch error:', error);
                results.innerHTML = `<h3>❌ Simple Fetch Error:</h3><pre>Error: ${error.message}\nType: ${error.constructor.name}\nStack: ${error.stack}</pre>`;
            }
        }
    </script>
</body>
</html>
